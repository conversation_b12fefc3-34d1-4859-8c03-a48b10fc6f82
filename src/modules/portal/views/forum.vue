<style lang="less">
@import "./tag.less";
</style>

<template>
  <div class="forum_tag">
    <div
      class="forum_tag_pc"
      v-if="!!isPc"
      style="height: 650px;overflow: hidden;background: #fff; "
    >
      <Card style="height: 650px;overflow: auto;">
        <h3>
          添加帖子类型标签
          <span
            @click="setViewTag"
            v-if="!!isWin"
            style="float: right;font-size: 12px;color: #ccc;font-weight: normal;cursor: pointer;"
            >浮层</span
          >
        </h3>

        <div class="thread">
          <div
            class="tags_all"
            style="border: 1px solid #efefef;border-radius: 5px;padding: 12px;margin-top: 10px;min-height: 60px;overflow: scroll;padding-top: 0;padding-right: 0;"
          >
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_1"
              :key="tag.index"
              v-if="tag_3_1.length > 0"
              @click="check_tag(tag, index, 3, 1)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_2"
              :key="tag.index"
              v-if="tag_3_2.length > 0"
              @click="check_tag(tag, index, 3, 2)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_3"
              :key="tag.index"
              v-if="tag_3_3.length > 0"
              @click="check_tag(tag, index, 3, 3)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br /><br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              :style="
                index === tag_3_4.length - 1 ? 'margin-right:0 !important' : ''
              "
              v-for="(tag, index) in tag_3_4"
              :key="tag.index"
              v-if="tag_3_4.length > 0"
              @click="check_tag(tag, index, 3, 4)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_5"
              :key="tag.index"
              v-if="tag_3_5.length > 0"
              @click="check_tag(tag, index, 3, 5)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_6"
              :key="tag.index"
              v-if="tag_3_6.length > 0"
              @click="check_tag(tag, index, 3, 6)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_7"
              :key="tag.index"
              v-if="tag_3_7.length > 0"
              @click="check_tag(tag, index, 3, 7)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <p style="color: #efefef;" v-if="tags_all.length === 0">
              还没有添加帖子类型标签...
            </p>
          </div>
          <br />
          <h3>添加学校名称标签</h3>
          <div class="add-tag">
            <Row>
              <Col span="20">
                <!--filterable-->
                <Select
                  v-model="add_tag_1.id"
                  filterable
                  remote
                  multiple
                  :remote-method="handleSearch_1"
                  :loading="loading"
                  @on-change="choseKey_1"
                  @on-query-change="changeKey_1"
                  placement="top"
                  placeholder="请输入帖子类型标签"
                  ref="store1"
                  clearable
                >
                  <Option
                    v-for="(item, index) in searchData_1"
                    :label="item.name + ',' + item.synonyms.join(',')"
                    label-in-value="true"
                    :value="item.id"
                    :key="index"
                    >{{ item.name }}</Option
                  ><!--、 v-html="item.name"-->
                </Select>
              </Col>
              <Col span="4"> </Col>
            </Row>
          </div>
          <div
            style="border: 1px solid #efefef;border-radius: 5px;padding: 12px;margin-top: 10px;min-height: 60px;overflow: scroll"
          >
            <div
              class="tag_list"
              v-for="(tag, index) in tags_1"
              :key="tag.index"
              v-if="tags_1.length > 0"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + (index >= 15 ? index - 14 : index + 1)">{{
                  tag.name
                }}</span>
                <div slot="content" v-if="tag.synonym">
                  <p v-for="(k, i) in tag.synonym" :key="k.index">
                    {{ k.name }}
                  </p>
                </div>
              </Tooltip>
              <Icon
                type="ios-close-empty"
                @click="del_tag(tag, index, 1)"
              ></Icon>
            </div>
            <p style="color: #efefef;" v-if="tags_1.length === 0">
              还没有添加帖子类型标签...
            </p>
          </div>
        </div>
        <br />
        <div class="school">
          <h3>添加专业方向标签</h3>
          <div
            class="tags_pro"
            style="border: 1px solid #efefef;border-radius: 5px;padding: 12px;margin-top: 10px;min-height: 60px;overflow: scroll;padding-top: 0;padding-right: 0;"
          >
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_2_1"
              :key="tag.index"
              v-if="tag_2_1.length > 0"
              @click="check_tag(tag, index, 2, 1)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_2_2"
              :key="tag.index"
              v-if="tag_2_2.length > 0"
              @click="check_tag(tag, index, 2, 2)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_2_3"
              :key="tag.index"
              v-if="tag_2_3.length > 0"
              @click="check_tag(tag, index, 2, 3)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <p style="color: #efefef;" v-if="tags_pro.length === 0">
              还没有添加帖子类型标签...
            </p>
          </div>
        </div>
        <div class="program"></div>
      </Card>
    </div>
    <div v-if="!isPc" class="forum_tag_mobile" style="overflow: auto;">
      <Card>
        <h3
          style="text-align: center;border-bottom: 1px solid #efefef;margin-bottom: 20px;"
        >
          帖子标签
        </h3>
        <div class="thread">
          <h3>添加帖子类型标签</h3>
          <div
            class="tags_all"
            style="border: 1px solid #efefef;border-radius: 5px;padding: 12px;margin-top: 10px;min-height: 60px;overflow: scroll"
          >
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_1"
              :key="tag.index"
              v-if="tag_3_1.length > 0"
              @click="check_tag(tag, index, 3, 1)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_2"
              :key="tag.index"
              v-if="tag_3_2.length > 0"
              @click="check_tag(tag, index, 3, 2)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_3"
              :key="tag.index"
              v-if="tag_3_3.length > 0"
              @click="check_tag(tag, index, 3, 3)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br /><br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_4"
              :key="tag.index"
              v-if="tag_3_4.length > 0"
              @click="check_tag(tag, index, 3, 4)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <div
              :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
              v-for="(tag, index) in tag_3_5"
              :key="tag.index"
              v-if="tag_3_5.length > 0"
              @click="check_tag(tag, index, 3, 5)"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
              </Tooltip>
            </div>
            <br />
            <p style="color: #efefef;" v-if="tags_all.length === 0">
              还没有添加帖子类型标签...
            </p>
          </div>
          <br />
          <h3>添加学校名称标签</h3>
          <div class="add-tag">
            <Row>
              <Col span="20">
                <Select
                  v-model="add_tag_1.id"
                  filterable
                  remote
                  multiple
                  :remote-method="handleSearch_1"
                  :loading="loading"
                  @on-change="choseKey_1"
                  @on-query-change="changeKey_1"
                  placement="top"
                  placeholder="请输入帖子类型标签"
                  ref="store1"
                  clearable
                >
                  <Option
                    v-for="(item, index) in searchData_1"
                    label-in-value="true"
                    :value="item.id"
                    :label="item.name + ',' + item.synonyms.join(',')"
                    :key="index"
                    v-html="item.name"
                  ></Option>
                </Select>
              </Col>
              <Col span="4">
                <Button v-if="!!isadd_1" @click="add_tags_1">添加</Button>
                <Button class="disabled" disabled v-if="!isadd_1">添加</Button>
              </Col>
            </Row>
          </div>
          <div
            style="border: 1px solid #efefef;border-radius: 5px;padding: 12px;margin-top: 10px;min-height: 60px;overflow: scroll"
          >
            <div
              class="tag_list"
              v-for="(tag, index) in tags_1"
              :key="tag.index"
              v-if="tags_1.length > 0"
            >
              <Tooltip
                placement="bottom"
                :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
              >
                <span :class="'bg' + (index >= 15 ? index - 14 : index + 1)">{{
                  tag.name
                }}</span>
                <div slot="content" v-if="tag.synonym">
                  <p v-for="(k, i) in tag.synonym" :key="k.index">
                    {{ k.name }}
                  </p>
                </div>
              </Tooltip>
              <Icon
                type="ios-close-empty"
                @click="del_tag(tag, index, 1)"
              ></Icon>
            </div>
            <p style="color: #efefef;" v-if="tags_1.length === 0">
              还没有添加帖子类型标签...
            </p>
          </div>
        </div>
        <br />
        <h3>添加专业方向标签</h3>
        <div
          class="tags_pro"
          style="border: 1px solid #efefef;border-radius: 5px;padding: 12px;margin-top: 10px;min-height: 60px;overflow: scroll;"
        >
          <div
            :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
            v-for="(tag, index) in tag_2_1"
            :key="tag.index"
            v-if="tag_2_1.length > 0"
            @click="check_tag(tag, index, 2, 1)"
          >
            <Tooltip
              placement="bottom"
              :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
            >
              <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
            </Tooltip>
          </div>
          <br />
          <div
            :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
            v-for="(tag, index) in tag_2_2"
            :key="tag.index"
            v-if="tag_2_2.length > 0"
            @click="check_tag(tag, index, 2, 2)"
          >
            <Tooltip
              placement="bottom"
              :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
            >
              <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
            </Tooltip>
          </div>
          <br />
          <div
            :class="!!tag.checked ? 'tag_list on' : 'tag_list'"
            v-for="(tag, index) in tag_2_3"
            :key="tag.index"
            v-if="tag_2_3.length > 0"
            @click="check_tag(tag, index, 2, 3)"
          >
            <Tooltip
              placement="bottom"
              :class="!!tag.synonym && tag.synonym.length > 0 ? '' : 'hide'"
            >
              <span :class="'bg' + tag.bg_type">{{ tag.name }}</span>
            </Tooltip>
          </div>
          <p style="color: #efefef;" v-if="tags_pro.length === 0">
            还没有添加帖子类型标签...
          </p>
        </div>
      </Card>
    </div>
  </div>
</template>
<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
export default {
  name: "forum_tag",
  data() {
    return {
      tags_all: [],
      tags_pro: [],
      tags_1: [],
      tags_2: [],
      tags_3: [],
      tid: "",
      searchData_1: [],
      searchData_2: [],
      searchData_3: [],
      add_tag_1: {
        name: "",
        id: "",
        main: 0,
        synonym_id: "",
      },
      add_tag_2: {
        name: "",
        id: "",
        main: 0,
        synonym_id: "",
      },
      showData_1: false,
      showData_2: false,
      showData_3: false,
      loading_1: false,
      loading_2: false,
      loading_3: false,
      queryText_1: "",
      queryText_2: "",
      queryText_3: "",
      isadd_1: false,
      isadd_2: false,
      isSearch_1: false,
      isSearch_2: false,
      loading: false,
      searchKeyword: [],
      isPc: true,
      istDel: false,
      tag_3_1: [],
      tag_3_2: [],
      tag_3_3: [],
      tag_3_4: [],
      tag_3_5: [],
      tag_3_6: [],
      tag_3_7: [],
      tag_2_1: [],
      tag_2_2: [],
      tag_2_3: [],
      tag_2_4: [],
      isWin: false,
    };
  },
  watch: {
    $route(to) {
      if (to.name === "forum_tag") {
        this.tags = [];
        this.tags_pro = [];
        this.tid = "";
        this.errMsg = "";
        this.getData();
      }
    },
  },
  mounted() {
    this.$store.commit("isPC");
    this.isPc = this.$store.state.register.ispc;

    this.getData();
    if (window.self === window.top) {
      this.isWin = true;
    } else {
      this.isWin = false;
    }
    document.onkeydown = function(event) {
      //esc  关掉tags浮窗
      var event = event || window.event;
      if (event.keyCode === 27) {
        window.parent.postMessage({ tag_type: "close_tag" }, "*");
      }
    };
    window.addEventListener("message", (e) => {
      if (e.data.viewTag === "true") {
        window.localStorage.setItem("viewTag", true);
      }
    });
  },
  methods: {
    getData() {
      this.tid = window.location.href.split("/").pop(); //1369941
      this.get_tags();
      this.get_all_tag();
      this.get_p_tag();
    },
    get_all_tag() {
      //  所有已有的标签
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/tag/forum/property/3",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          //console.log(res.data.data)
          if (res.data.msg === "success") {
            _this.tags_all = res.data.data;
            var n = 0;
            for (var i = 0; i < _this.tags_all.length; i++) {
              n++;
              if (n >= 15) {
                n = n - 14;
              }
              _this.tags_all[i].bg_type = n;
              //_this.tags_all[i].bg_type = i >= 15 ? (i - 14) : (i+1);
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    set_tag_3() {
      this.tag_3_1 = [];
      this.tag_3_2 = [];
      this.tag_3_3 = [];
      this.tag_3_4 = [];
      this.tag_3_5 = [];
      this.tag_3_6 = [];
      this.tag_3_7 = [];
      for (var i = 0; i < this.tags_all.length; i++) {
        if (this.tags_all[i].order.split(".")[1] === "0") {
          var item = JSON.parse(JSON.stringify(this.tags_all[i]));
          this.tag_3_1.push(item);
        } else if (this.tags_all[i].order.split(".")[1] === "1") {
          var item = JSON.parse(JSON.stringify(this.tags_all[i]));
          this.tag_3_2.push(item);
        } else if (this.tags_all[i].order.split(".")[1] === "2") {
          var item = JSON.parse(JSON.stringify(this.tags_all[i]));
          this.tag_3_3.push(item);
        } else if (this.tags_all[i].order.split(".")[1] === "3") {
          var item = JSON.parse(JSON.stringify(this.tags_all[i]));
          this.tag_3_4.push(item);
        } else if (this.tags_all[i].order.split(".")[1] === "4") {
          var item = JSON.parse(JSON.stringify(this.tags_all[i]));
          this.tag_3_5.push(item);
        } else if (this.tags_all[i].order.split(".")[1] === "5") {
          var item = JSON.parse(JSON.stringify(this.tags_all[i]));
          this.tag_3_6.push(item);
        } else if (this.tags_all[i].order.split(".")[1] === "6") {
          var item = JSON.parse(JSON.stringify(this.tags_all[i]));
          this.tag_3_7.push(item);
        } else {
          var item = JSON.parse(JSON.stringify(this.tags_all[i]));
          item.order = item.order.length > 0 ? item.order : "99.99";
          this.tag_3_6.push(item);
        }
      }
      this.tag_3_1 = this.sort_list(this.tag_3_1);
      this.tag_3_2 = this.sort_list(this.tag_3_2);
      this.tag_3_3 = this.sort_list(this.tag_3_3);
      this.tag_3_4 = this.sort_list(this.tag_3_4);
      this.tag_3_5 = this.sort_list(this.tag_3_5);
      this.tag_3_6 = this.sort_list(this.tag_3_6);
      this.tag_3_7 = this.sort_list(this.tag_3_7);
    },
    get_tags() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/tag/forum/" + this.tid,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          //console.log(res)
          if (res.data.msg === "success") {
            _this.tags_1 = res.data.data.one;
            _this.tags_2 = res.data.data.two;
            _this.tags_3 = res.data.data.three;

            setTimeout(function() {
              _this.checked_list();
              _this.set_tag_3();
              _this.set_tag_2();
            }, 300);
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    get_p_tag() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/tag/forum/property/2",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          //console.log(res.data.data)
          if (res.data.msg === "success") {
            _this.tags_pro = res.data.data;
            var n = 0;
            for (var i = 0; i < _this.tags_pro.length; i++) {
              n++;
              if (n >= 15) {
                n = n - 14;
              }
              _this.tags_pro[i].bg_type = n;
              //_this.tags_all[i].bg_type = i >= 15 ? (i - 14) : (i+1);
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    set_tag_2() {
      this.tag_2_1 = [];
      this.tag_2_2 = [];
      this.tag_2_3 = [];
      for (var i = 0; i < this.tags_pro.length; i++) {
        if (this.tags_pro[i].order.split(".")[1] === "0") {
          var item = JSON.parse(JSON.stringify(this.tags_pro[i]));
          this.tag_2_1.push(item);
        } else if (this.tags_pro[i].order.split(".")[1] === "1") {
          var item = JSON.parse(JSON.stringify(this.tags_pro[i]));
          this.tag_2_2.push(item);
        } else if (this.tags_pro[i].order.split(".")[1] === "2") {
          var item = JSON.parse(JSON.stringify(this.tags_pro[i]));
          this.tag_2_3.push(item);
        } else {
          var item = JSON.parse(JSON.stringify(this.tags_pro[i]));
          item.order = item.order.length > 0 ? item.order : "99.99";
          this.tag_2_3.push(item);
        }
      }

      this.tag_2_1 = this.sort_list(this.tag_2_1);
      this.tag_2_2 = this.sort_list(this.tag_2_2);
      this.tag_2_3 = this.sort_list(this.tag_2_3);
    },
    sort_list(arr) {
      var l = arr.map((row) => {
        var r = row.order.split(".");
        r = r.map((el) => el.padStart(2, "0"));
        row.order = r.join(".");
        return row;
      });
      return l.sort((a, b) => (a.order > b.order ? 1 : -1));
    },
    checked_list() {
      for (var i = 0; i < this.tags_3.length; i++) {
        for (var j = 0; j < this.tags_all.length; j++) {
          //this.tags_all[j].checked = false;
          if (this.tags_3[i].id === this.tags_all[j].id) {
            // this.tags_all[j].checked = true;
            //this.$set
            this.$set(this.tags_all[j], "checked", true);
          }
        }
      }
      for (var i = 0; i < this.tags_2.length; i++) {
        for (var j = 0; j < this.tags_pro.length; j++) {
          if (this.tags_2[i].id === this.tags_pro[j].id) {
            this.$set(this.tags_pro[j], "checked", true);
          }
        }
      }
    },
    check_tag(obj, index, n, m) {
      var dataIn = {
        tid: this.tid,
        tags: [],
      };
      var method = "";
      var checked = false;
      if (!obj.checked) {
        if (n === 3) {
          var tag = {
            tagid: obj.id,
            action: "bind",
            property: 3,
          };
        } else if (n === 2) {
          var tag = {
            tagid: obj.id,
            action: "bind",
            property: 2,
          };
        }

        dataIn.tags.push(tag);
        method = "POST";
        checked = true;
      } else {
        if (n === 3) {
          dataIn = {
            tid: this.tid,
            tagid: obj.id,
            property: 3,
          };
        } else if (n === 2) {
          dataIn = {
            tid: this.tid,
            tagid: obj.id,
            property: 2,
          };
        }

        method = "DELETE";
        checked = false;
      }
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/tag/forum",
          method: method,
          data: dataIn,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            if (!!checked) {
              if (n === 3) {
                //_this.tags_all[index].checked = true;
                if (m === 1) {
                  _this.tag_3_1[index].checked = true;
                } else if (m === 2) {
                  _this.tag_3_2[index].checked = true;
                } else if (m === 3) {
                  _this.tag_3_3[index].checked = true;
                } else if (m === 4) {
                  _this.tag_3_4[index].checked = true;
                } else if (m === 5) {
                  _this.tag_3_5[index].checked = true;
                } else if (m === 6) {
                  _this.tag_3_6[index].checked = true;
                } else if (m === 7) {
                  _this.tag_3_7[index].checked = true;
                }
              } else if (n === 2) {
                //_this.tags_pro[index].checked = true;
                if (m === 1) {
                  _this.tag_2_1[index].checked = true;
                } else if (m === 2) {
                  _this.tag_2_2[index].checked = true;
                } else if (m === 3) {
                  _this.tag_2_3[index].checked = true;
                }
                /*else if(m === 4){
                                    _this.tag_2_4[index].checked = true;
                                }*/
              }
            } else {
              if (n === 3) {
                //_this.tags_all[index].checked = false;
                if (m === 1) {
                  _this.tag_3_1[index].checked = false;
                } else if (m === 2) {
                  _this.tag_3_2[index].checked = false;
                } else if (m === 3) {
                  _this.tag_3_3[index].checked = false;
                } else if (m === 4) {
                  _this.tag_3_4[index].checked = false;
                } else if (m === 5) {
                  _this.tag_3_5[index].checked = false;
                } else if (m === 6) {
                  _this.tag_3_6[index].checked = false;
                } else if (m === 7) {
                  _this.tag_3_7[index].checked = false;
                }
              } else if (n === 2) {
                //_this.tags_pro[index].checked = false;
                if (m === 1) {
                  _this.tag_2_1[index].checked = false;
                } else if (m === 2) {
                  _this.tag_2_2[index].checked = false;
                } else if (m === 3) {
                  _this.tag_2_3[index].checked = false;
                }
                /*else if(m === 4){
                                    _this.tag_2_4[index].checked = false;
                                }*/
              }
            }

            var list_data = [];
            if (n === 3) {
              list_data = JSON.parse(JSON.stringify(_this.tags_all));
              _this.tags_all = [];
              _this.tags_all = JSON.parse(JSON.stringify(list_data));
            } else if (n === 2) {
              list_data = JSON.parse(JSON.stringify(_this.tags_pro));
              _this.tags_pro = [];
              _this.tags_pro = JSON.parse(JSON.stringify(list_data));
            }
          } else {
            //console.log(res.data);
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          //console.log(err)
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    add_tags_1() {
      var dataIn = {
        tags: [],
        tid: this.tid,
      };
      if (
        (this.add_tag_1.id === "" || this.add_tag_1.id.length === 0) &&
        this.add_tag_1.name !== ""
      ) {
        if (!this.istDel) {
          var tags = this.add_tag_1.name.replace(/，/gi, ",").split(","); // //  中文逗号替换成英文逗号
          for (var i = 0; i < tags.length; i++) {
            var item = {
              name: tags[i],
              action: "create",
              property: 1,
              // main: i===0 ? 1 : 0
            };
            dataIn.tags.push(item);
          }
        } else {
          this.$refs.store1.clearSingleSelect(); // 清空
        }
      } else if (this.add_tag_1.id.length > 0 && this.add_tag_1.name === "") {
        var tagid = parseInt(this.add_tag_1.id[0]);
        if (isCanAdd(tagid, this.tags_1)) {
          var item = {
            tagid: tagid,
            action: "bind",
            property: 1,
          };
          dataIn.tags.push(item);
        } else {
          this.queryText_1 = "";
          this.add_key_1 = {};
          this.searchData_1 = [];
          this.isadd_1 = false;
          this.$Notice.error({
            title: "当前帖子已经添加过此标签了！",
          });
          this.$refs.store1.clearSingleSelect(); // 清空
          this.isSearch_1 = true;
        }
      } else if (this.add_tag_1.id.length > 0 && this.add_tag_1.name !== "") {
        var tagid = this.add_tag_1.id[0];
        if (isCanAdd(tagid, this.tags_1)) {
          var item = {
            tagid: tagid,
            action: "bind",
            property: 1,
          };
          dataIn.tags.push(item);
        } else {
          this.istDel = true;
          this.$Notice.error({
            title: "当前帖子已经添加过此标签了！",
          });
          this.$refs.store1.clearSingleSelect(); // 清空
          return false;
        }
      }
      //console.log(dataIn)
      if (!dataIn.tags.length) {
        return false;
      }
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/tag/forum",
          method: "POST",
          data: dataIn,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.get_tags();
            _this.queryText_1 = "";
            _this.searchData_1 = [];
            _this.add_key_1 = {};
            _this.isadd_1 = false;
            _this.isSearch_1 = true;
            _this.$refs.store1.clearSingleSelect(); // 清空
          } else {
            //console.log(res.data);
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          //console.log(err)
          _this.$Notice.error({
            title: err.msg,
          });
        });
      function isCanAdd(kid, kw) {
        for (var i = 0; i < kw.length; i++) {
          if (kid === kw[i].id) {
            return false;
          }
        }
        return true;
      }
      function isIn(str, pieces) {
        for (var j = 0; j < pieces.length; j++) {
          if (str === pieces[j].keyword) {
            return false;
          }
        }
        return true;
      }
    },
    add_tags_2() {
      var dataIn = {
        tags: [],
        tid: this.tid,
      };
      if (
        (this.add_tag_2.id === "" || this.add_tag_2.id.length === 0) &&
        this.add_tag_2.name !== ""
      ) {
        var tags = this.add_tag_2.name.replace(/，/gi, ",").split(","); // //  中文逗号替换成英文逗号
        for (var i = 0; i < tags.length; i++) {
          var item = {
            name: tags[i],
            action: "create",
            property: 2,
            // main: i===0 ? 1 : 0
          };
          dataIn.tags.push(item);
        }
      } else if (this.add_tag_2.id.length > 0 && this.add_tag_2.name === "") {
        var tagid = parseInt(this.add_tag_2.id[0]);
        if (isCanAdd(tagid, this.tags_2)) {
          var item = {
            tagid: tagid,
            action: "bind",
            property: 2,
          };
          dataIn.tags.push(item);
        } else {
          this.queryText_2 = "";
          this.add_key_2 = {};
          this.searchData_2 = [];
          this.isadd_2 = false;
          this.$Notice.error({
            title: "当前帖子已经添加过此标签了！",
          });
          this.$refs.store2.clearSingleSelect(); // 清空
          this.isSearch_2 = true;
        }
      } else if (this.add_tag_2.id.length > 0 && this.add_tag_2.name !== "") {
        console.log(this.add_tag_2);
        var tagid = this.add_tag_2.id[0];
        console.log(isCanAdd(tagid, this.tags_2));
        if (isCanAdd(tagid, this.tags_2)) {
          var item = {
            tagid: tagid,
            action: "bind",
            property: 2,
          };
          dataIn.tags.push(item);
          var tags = this.add_tag_2.name.replace(/，/gi, ",").split(","); //  中文逗号替换成英文逗号
          for (var i = 0; i < tags.length; i++) {
            var item = {
              name: tags[i],
              action: "create",
              property: 2,
              // main: i===0 ? 1 : 0
            };
            dataIn.tags.push(item);
          }
        } else {
          var tags = this.add_tag_2.name.replace(/，/gi, ",").split(","); //  中文逗号替换成英文逗号
          for (var i = 0; i < tags.length; i++) {
            var item = {
              name: tags[i],
              action: "create",
              property: 2,
              // main: i===0 ? 1 : 0
            };
            dataIn.tags.push(item);
          }
        }
      }
      console.log(dataIn);
      if (!dataIn.tags.length) {
        return false;
      }
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/tag/forum",
          method: "POST",
          data: dataIn,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.get_tags();
            _this.queryText_2 = "";
            _this.searchData_2 = [];
            _this.add_key_2 = {};
            _this.isadd_2 = false;
            _this.isSearch_2 = true;
            _this.$refs.store2.clearSingleSelect(); // 清空
          } else {
            //console.log(res.data);
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          //console.log(err)
          _this.$Notice.error({
            title: err.msg,
          });
        });
      function isCanAdd(kid, kw) {
        for (var i = 0; i < kw.length; i++) {
          if (kid === kw[i].id) {
            return false;
          }
        }
        return true;
      }
      function isIn(str, pieces) {
        for (var j = 0; j < pieces.length; j++) {
          if (str === pieces[j].keyword) {
            return false;
          }
        }
        return true;
      }
    },
    del_tag(obj, index, type) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/tag/forum",
          method: "DELETE",
          data: {
            tid: this.tid,
            tagid: obj.id,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            //console.log(res.data)
            if (type === 1) {
              _this.tags_1.splice(index, 1);
            } else if (type === 2) {
              _this.tags_2.splice(index, 1);

              var list_data = [];
              list_data = JSON.parse(JSON.stringify(_this.tags_2));
              _this.tags_2 = [];
              _this.tags_2 = JSON.parse(JSON.stringify(list_data));
            }
          } else {
            //console.log(res.data);
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          //console.log(err)
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    handleSearch_1(query) {
      //console.log(query)
      this.searchData_1 = "";
      var _this = this;
      if (query !== "") {
        util
          .ajax({
            url: "/api/v1/admin/tag/forum/search",
            method: "POST",
            data: {
              s: query,
              property: 1,
            },
          })
          .then(function(res) {
            if (res.data.msg === "success") {
              //console.log(res.data.data)
              if (res.data.data.length > 0) {
                _this.searchData_1 = res.data.data;
                for (var i = 0; i < _this.searchData_1.length; i++) {
                  var subs = [];
                  if (
                    !!_this.searchData_1[i].synonym &&
                    _this.searchData_1[i].synonym.length > 0
                  ) {
                    for (
                      var j = 0;
                      j < _this.searchData_1[i].synonym.length;
                      j++
                    ) {
                      subs.push(_this.searchData[i].synonym[j].keyword);
                    }
                    console.log(query);
                  }
                  if (subs.length > 0) {
                    _this.searchData_1[i].name =
                      _this.searchData_1[i].name + "，" + subs.join("，");
                  } else {
                    _this.searchData_1[i].name = _this.searchData_1[i].name;
                  }
                  if (!_this.searchData_1[i].synonyms) {
                    _this.searchData_1[i].synonyms = [];
                  }
                  _this.searchData_1[i].synonyms.push(query);
                }
              }
            } else {
              _this.$Notice.error({
                title: res.data.msg,
              });
            }
          })
          .catch(function(err) {
            console.log(err);
            _this.$Notice.error({
              title: err.msg,
            });
          });
      } else {
        this.searchData_1 = [];
      }
    },
    handleSearch_2(query) {
      //console.log(query)
      this.searchData_2 = "";
      var _this = this;
      if (query !== "") {
        util
          .ajax({
            url: "/api/v1/admin/tag/forum/search", //?s=' + this.add_key.key,
            method: "POST",
            data: {
              s: query,
              property: 2,
            },
          })
          .then(function(res) {
            if (res.data.msg === "success") {
              //console.log(res.data.data)
              if (res.data.data.length > 0) {
                _this.searchData_2 = res.data.data;
                for (var i = 0; i < _this.searchData_2.length; i++) {
                  var subs = [];
                  if (
                    !!_this.searchData_2[i].synonym_ &&
                    _this.searchData_2[i].synonym.length > 0
                  ) {
                    for (
                      var j = 0;
                      j < _this.searchData_2[i].synonym.length;
                      j++
                    ) {
                      subs.push(_this.searchData_2[i].synonym[j].keyword);
                    }
                  }
                  if (subs.length > 0) {
                    _this.searchData_2[i].name =
                      _this.searchData_2[i].name + "，" + subs.join("，"); //+ '<span style="float: right">'+ _this.searchData[i].id +'<span>';
                  } else {
                    _this.searchData_2[i].name = _this.searchData_2[i].name; //+ '<span style="float: right">'+ _this.searchData[i].id +'<span>'
                  }
                }
              }
            } else {
              //console.log(res.data);
              _this.$Notice.error({
                title: res.data.msg,
              });
            }
          })
          .catch(function(err) {
            console.log(err);
            _this.$Notice.error({
              title: err.msg,
            });
          });
      } else {
        this.searchData_2 = [];
      }
    },
    choseKey_1(tagid) {
      //console.log(tagid)
      if (!tagid) {
        return false;
      }
      if (tagid.length > 0) {
        this.isadd_1 = true;
      } else {
        this.isadd_1 = false;
      }
      //console.log(this.add_tag)
      if (this.add_tag_1.id.length === 1) {
        //this.add_tag.name = '';
      } else if (this.add_tag_1.id.length > 1) {
        var new_tag = this.add_tag_1.id[1];
        this.add_tag_1.id = [];
        this.add_tag_1.id.push(new_tag);
      }

      this.add_tags_1();
    },
    choseKey_2(tagid) {
      if (!tagid) {
        return false;
      }
      if (tagid.length > 0) {
        this.isadd_2 = true;
      } else {
        this.isadd_2 = false;
      }
      //console.log(this.add_tag)
      if (this.add_tag_2.id.length === 1) {
        //this.add_tag_2.name = '';
      } else if (this.add_tag_2.id.length > 1) {
        var new_tag = this.add_tag_2.id[1];
        this.add_tag_2.id = [];
        this.add_tag_2.id.push(new_tag);
      }
    },
    changeKey_1(q) {
      // console.log(g)
      this.queryText_1 = q;
      this.add_tag_1.name = q;
      if (q === "" && (!this.add_tag_1.id || this.add_tag_1.id.length === 0)) {
        this.$refs.store1.clearSingleSelect(); // 清空
        this.isadd_1 = false;
        return false;
      } else {
        this.isadd_1 = true;
      }
    },
    changeKey_2(q) {
      //console.log(q)
      this.queryText_2 = q;
      this.add_tag_2.name = q;
      if (q === "" && (!this.add_tag_2.id || this.add_tag_2.id.length === 0)) {
        this.$refs.store2.clearSingleSelect(); // 清空
        this.isadd_2 = false;
      } else {
        this.isadd_2 = true;
      }
    },
    setViewTag() {
      window.localStorage.setItem("viewTag", false);
      window.close();
    },
  },
};
</script>
